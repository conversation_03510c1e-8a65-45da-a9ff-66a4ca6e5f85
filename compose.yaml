x-customizable-image: &customizable_image
  # By default the image used only contains the `frappe` and `erpnext` apps.
  # See https://github.com/frappe/frappe_docker/blob/main/docs/custom-apps.md
  # about using custom images.
  image: ${CUSTOM_IMAGE:-frappe/erpnext}:${CUSTOM_TAG:-$ERPNEXT_VERSION}
  pull_policy: ${PULL_POLICY:-always}

x-depends-on-configurator: &depends_on_configurator
  depends_on:
    configurator:
      condition: service_completed_successfully

x-backend-defaults: &backend_defaults
  <<: [*depends_on_configurator, *customizable_image]
  volumes:
    - sites:/home/<USER>/frappe-bench/sites

services:
  configurator:
    <<: *backend_defaults
    platform: linux/amd64
    entrypoint:
      - bash
      - -c
    # add redis_socketio for backward compatibility
    command:
      - >
        ls -1 apps > sites/apps.txt;
        bench set-config -g db_host $$DB_HOST;
        bench set-config -gp db_port $$DB_PORT;
        bench set-config -g redis_cache "redis://$$REDIS_CACHE";
        bench set-config -g redis_queue "redis://$$REDIS_QUEUE";
        bench set-config -g redis_socketio "redis://$$REDIS_QUEUE";
        bench set-config -gp socketio_port $$SOCKETIO_PORT;
    environment:
      DB_HOST: ${DB_HOST:-}
      DB_PORT: ${DB_PORT:-}
      REDIS_CACHE: ${REDIS_CACHE:-}
      REDIS_QUEUE: ${REDIS_QUEUE:-}
      SOCKETIO_PORT: 9000
    depends_on: {}

  backend:
    <<: *backend_defaults
    platform: linux/amd64

  frontend:
    <<: *customizable_image
    platform: linux/amd64
    command:
      - nginx-entrypoint.sh
    environment:
      BACKEND: backend:8000
      SOCKETIO: websocket:9000
      FRAPPE_SITE_NAME_HEADER: ${FRAPPE_SITE_NAME_HEADER:-$$host}
      UPSTREAM_REAL_IP_ADDRESS: ${UPSTREAM_REAL_IP_ADDRESS:-127.0.0.1}
      UPSTREAM_REAL_IP_HEADER: ${UPSTREAM_REAL_IP_HEADER:-X-Forwarded-For}
      UPSTREAM_REAL_IP_RECURSIVE: ${UPSTREAM_REAL_IP_RECURSIVE:-off}
      PROXY_READ_TIMEOUT: ${PROXY_READ_TIMEOUT:-120}
      CLIENT_MAX_BODY_SIZE: ${CLIENT_MAX_BODY_SIZE:-50m}
    volumes:
      - sites:/home/<USER>/frappe-bench/sites
    depends_on:
      - backend
      - websocket

  websocket:
    <<: [*depends_on_configurator, *customizable_image]
    platform: linux/amd64
    command:
      - node
      - /home/<USER>/frappe-bench/apps/frappe/socketio.js
    volumes:
      - sites:/home/<USER>/frappe-bench/sites

  queue-short:
    <<: *backend_defaults
    platform: linux/amd64
    command: bench worker --queue short,default

  queue-long:
    <<: *backend_defaults
    platform: linux/amd64
    command: bench worker --queue long,default,short

  scheduler:
    <<: *backend_defaults
    platform: linux/amd64
    command: bench schedule

# ERPNext requires local assets access (Frappe does not)
volumes:
  sites:
