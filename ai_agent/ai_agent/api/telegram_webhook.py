# frappe-bench/apps/ai_agent/ai_agent/ai_agent/api/telegram_webhook.py
import frappe
import requests
import json
import uuid
import os
from datetime import datetime, timezone
from http import HTTPStatus
from ai_agent.telegram_integration.telegram_integration import (
    create_or_update_room, create_message, upload_to_frappe, format_message, format_timestamp
)


# Конфігурація синк-сервера
SYNC_SERVER_URL = "https://hook.eu1.make.celonis.com/otxdergqe6wo89u7xvzbqnivuvsvknal"
SYNC_AUTHORIZATION = "Bearer 16a31a9a463ee80fadf7045aa27fd66601f70eba250b53dc2b7aef3824af1809"

@frappe.whitelist(allow_guest=True)
def webhook():
    """
    Ендпоінт для обробки вхідних оновлень від Telegram Bot API.
    """
    try:
        update = frappe.request.get_json()
        frappe.logger().info(f"WEBHOOK RECEIVED: {update}")

        if not update or "message" not in update:
            frappe.log_error("No message in update", "Telegram Webhook")
            frappe.logger().info("WEBHOOK: No message in update")
            return {"status": "ignored", "reason": "No message in update"}

        message = update["message"]
        chat_id = str(message.get("chat", {}).get("id"))
        message_id = str(message.get("message_id"))
        from_user = message.get("from", {})
        user_id = str(from_user.get("id"))
        user_name = from_user.get("first_name") or from_user.get("username") or "UnknownUser"
        text = message.get("text", "")
        timestamp = datetime.utcfromtimestamp(message.get("date")).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        reply_to_message_id = str(message.get("reply_to_message", {}).get("message_id", "")) or None

        frappe.logger().info(f"WEBHOOK PROCESSING MESSAGE: message_id={message_id}, chat_id={chat_id}, user_id={user_id}, text='{text}'")

        # Формуємо room_id та user_id для Telegram
        room_id = f"{chat_id}:telegram.org"
        user_id = f"{user_id}:telegram.org"
        account_id = "**********"  # ID бота kattiecatlina
        agent_name = "kattiecatlina"
        synk_id = str(uuid.uuid4())

        # Обробка медіа (лише фото для прикладу)
        media_url = None
        media_type = None
        bot_token = "<YourBotToken>"  # Замініть на токен бота, якщо він стане доступним
        if "photo" in message:
            file_id = message["photo"][-1]["file_id"]  # Найвища роздільна здатність
            file_response = requests.get(f"https://api.telegram.org/bot{bot_token}/getFile?file_id={file_id}")
            file_path = file_response.json().get("result", {}).get("file_path")
            if file_path:
                media_url = f"https://api.telegram.org/file/bot{bot_token}/{file_path}"
                media_type = "image"
                # Завантажуємо до Frappe
                temp_file = f"temp_photo_{message_id}.jpg"
                response = requests.get(media_url)
                with open(temp_file, "wb") as f:
                    f.write(response.content)
                media_url = upload_to_frappe(temp_file, f"photo_{message_id}.jpg", message_id)
                try:
                    os.remove(temp_file)
                except Exception as e:
                    frappe.log_error(f"Error removing temp file: {str(e)}", "Telegram Webhook")

        # Створюємо або оновлюємо кімнату
        room_id = create_or_update_room(chat_id, user_name, agent_name)
        if not room_id:
            frappe.log_error(f"Failed to create room for chat_id {chat_id}", "Telegram Webhook")
            return {"status": "error", "reason": "Failed to create room"}

        # Зберігаємо повідомлення в Message DocType
        message_doc = create_message(
            chat_id=chat_id,
            room_id=room_id,
            message_id=message_id,
            sender_id=user_id,
            sender_name=user_name,
            text=text or "No text",
            media_url=media_url,
            agent_name=agent_name,
            media_type=media_type,
            timestamp=datetime.fromisoformat(timestamp.replace("Z", "+00:00")),
            receiver_id=account_id,
            client=None,
            reply_to_message_id=reply_to_message_id,
            synk_id=synk_id
        )

        if not message_doc:
            frappe.log_error(f"Failed to create message {message_id}", "Telegram Webhook")
            return {"status": "error", "reason": "Failed to create message"}

        # Формуємо payload для синк-сервера
        sync_payload = [format_message(
            room_id=room_id,
            account_id=account_id,
            user_id=user_id,
            user_name=user_name,
            content_text=text,
            media_url=media_url,
            message_id=message_id,
            timestamp=datetime.fromisoformat(timestamp.replace("Z", "+00:00")),
            reply_to_message_id=reply_to_message_id,
            social_media_channel="telegram",
            synk_id=synk_id
        )]

        # Відправляємо на синк-сервер
        headers = {
            "Authorization": SYNC_AUTHORIZATION,
            "Content-Type": "application/json"
        }
        response = requests.post(SYNC_SERVER_URL, headers=headers, json=sync_payload)
        if response.status_code == 200:
            frappe.db.set_value("Message", message_doc.name, "status", "Synced")
            frappe.db.commit()
            frappe.log_error(f"Message {message_id} synced successfully", "Telegram Webhook")
            return {"status": "success", "message_id": message_id, "synk_id": synk_id}
        else:
            frappe.log_error(f"Sync failed for message {message_id}: {response.text}", "Telegram Webhook")
            return {"status": "error", "reason": response.text}

    except Exception as e:
        frappe.log_error(f"Webhook error: {str(e)}", "Telegram Webhook")
        return {"status": "error", "reason": str(e)}
