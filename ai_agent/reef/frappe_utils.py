# add_seren_to_frappe_min.py
import os, sys
import frappe

SITE_NAME  = "32016-51127.bacloud.info"
BENCH_PATH = "/home/<USER>/frappe-bench"
SITES_PATH = os.path.join(BENCH_PATH, "sites")
APPS_PATH  = os.path.join(BENCH_PATH, "apps")
sys.path.extend([BENCH_PATH, APPS_PATH, os.path.join(APPS_PATH, "frappe")])

def _discover_bench_and_sites():
    """Discover bench and sites paths relative to current environment."""
    return BENCH_PATH, SITES_PATH

# Global flag to track if Frappe has been initialized
_frappe_initialized = False

def _ensure_frappe_ready():
    """Initialize Frappe deterministically regardless of CWD.
    - Discover bench/sites relative to this file.
    - Export FRAPPE_SITE and FRAPPE_SITES_PATH for Frappe internals.
    - Ensure app and site log dirs exist *before* frappe.connect() to prevent logger errors.
    """
    global _frappe_initialized

    # Skip if already initialized
    if _frappe_initialized:
        return

    bench_path, sites_path = _discover_bench_and_sites()

    # Export canonical env vars so Frappe resolves paths correctly
    os.environ.setdefault("FRAPPE_SITE", SITE_NAME)
    os.environ.setdefault("FRAPPE_SITES_PATH", sites_path)
    os.environ.setdefault("SITES_HOME", sites_path)  # legacy/compat

    # Note: reef CLI now ensures we're already in frappe-bench directory
    print(f"Current working directory: {os.getcwd()}")
    print(f"Expected bench path: {bench_path}")

    # Ensure sys.path contains discovered bench locations
    if bench_path not in sys.path:
        sys.path.append(bench_path)
    apps_path = os.path.join(bench_path, "apps")
    if apps_path not in sys.path:
        sys.path.append(apps_path)

    # Create standard Frappe log directories (relative paths since we're in bench dir)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("apps/logs", exist_ok=True)
    os.makedirs(f"sites/{SITE_NAME}/logs", exist_ok=True)
    print("✓ Created standard Frappe log directories")

    # Initialize/connect once. Use discovered sites_path.
    try:
        if not getattr(frappe.local, "site", None):
            print(f"Initializing Frappe with site: {SITE_NAME}")
            frappe.init(site=SITE_NAME, sites_path=sites_path)

        if not getattr(frappe.local, "db", None):
            print("Connecting to Frappe database...")
            frappe.connect()

        print("✓ Frappe initialization completed successfully")

    except Exception as e:
        print(f"ERROR during Frappe initialization: {e}")
        print(f"Current working directory: {os.getcwd()}")
        raise

    # Mark as initialized
    _frappe_initialized = True

# Don't call initialization at import time - let functions call it when needed
# _ensure_frappe_ready()

def add_seren_to_frappe(agent: dict) -> dict:
	# Ensure Frappe is properly initialized before using it
	_ensure_frappe_ready()

	docname = agent.get("name") or agent.get("agent_name")
	if not docname:
		raise ValueError("agent must include 'agent_name' or 'name'")

	if frappe.db.exists("Agents", docname):
		doc = frappe.get_doc("Agents", docname)
		doc.agent_name = agent.get("agent_name") or docname
		doc.title = agent.get("title")
		doc.telegram_api_hash = agent.get("telegram_api_hash")
		doc.telegram_session_name = agent.get("telegram_session_name")
		doc.telegram_api_id = agent.get("telegram_api_id")
		doc.telegram_phone = agent.get("telegram_phone")
		doc.disabled = 1
		doc.save(ignore_permissions=True)
		frappe.db.commit()
	else:
		doc = frappe.new_doc("Agents")
		doc.name = docname
		doc.flags.name_set_by_user = True
		doc.agent_name = agent.get("agent_name") or docname
		doc.title = agent.get("title")
		doc.telegram_api_hash = agent.get("telegram_api_hash")
		doc.telegram_session_name = agent.get("telegram_session_name")
		doc.telegram_api_id = agent.get("telegram_api_id")
		doc.telegram_phone = agent.get("telegram_phone")
		doc.disabled = 1
		doc.insert(ignore_permissions=True, ignore_if_duplicate=True)
		frappe.db.commit()

	return {"name": doc.name}
