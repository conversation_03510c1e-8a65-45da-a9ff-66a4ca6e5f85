def add_seren_to_frappe(base_url: str, auth_token: str, agent: dict) -> dict:
    import requests

    headers = {
        "Authorization": f"token {auth_token}",
        "Content-Type": "application/json",
    }

    base = base_url.rstrip('/')

    # Decide target document name (ID)
    docname = agent.get("name") or agent.get("agent_name")
    if not docname:
        raise ValueError("agent must include 'agent_name' or 'name' to determine document ID")

    # Prepare payload: only include required/used fields (skip None), always disabled
    fields = {
        "name": docname,  # attempt to set explicit docname on creation
        "agent_name": agent.get("agent_name") or docname,
        "title": agent.get("title"),
        "telegram_api_hash": agent.get("telegram_api_hash"),
        "telegram_session_name": agent.get("telegram_session_name"),
        "telegram_api_id": agent.get("telegram_api_id"),
        "telegram_phone": agent.get("telegram_phone"),
        "disabled": 1,
    }

    payload = {k: v for k, v in fields.items() if v is not None}

    # POST create (let caller handle/log any errors)
    res = requests.post(
        f"{base}/api/resource/Agents",
        headers=headers,
        json=payload,
        timeout=15,
    )
    res.raise_for_status()
    return res.json()
