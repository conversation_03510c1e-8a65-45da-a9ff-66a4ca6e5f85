#!/usr/bin/env python3
"""
Reef CLI - Command-line interface for managing Telegram workers.
"""
import asyncio
import os
import typer
from typing import List, Optional, Dict, Any
from pathlib import Path
from rich.console import Console
from rich.table import Table, box
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn

# Frappe bench configuration
FRAPPE_BENCH_PATH = "/home/<USER>/frappe-bench"

def ensure_frappe_bench_cwd():
    """Ensure we're running from the frappe-bench directory.

    This is critical for Frappe's logging system to work correctly,
    as it uses relative paths for log file creation.
    """
    if not os.path.exists(FRAPPE_BENCH_PATH):
        console.print(f"[red]Error: Frappe bench directory not found at {FRAPPE_BENCH_PATH}[/red]")
        console.print("[yellow]Make sure you're running this in a Frappe environment.[/yellow]")
        raise typer.Exit(1)

    current_cwd = os.getcwd()
    if current_cwd != FRAPPE_BENCH_PATH:
        console.print(f"[blue]Changing working directory from {current_cwd} to {FRAPPE_BENCH_PATH}[/blue]")
        try:
            os.chdir(FRAPPE_BENCH_PATH)
            console.print(f"[green]✓ Now running from: {os.getcwd()}[/green]")
        except Exception as e:
            console.print(f"[red]Error: Could not change to frappe-bench directory: {e}[/red]")
            raise typer.Exit(1)


def config_path(name="tg-crew.yml"):
    """Find the configuration file by searching up the directory tree.

    Args:
        name: Name of the config file to find (default: tg-crew.yml)

    Returns:
        Path: Path to the found config file

    Raises:
        FileNotFoundError: If the config file is not found in any parent directory
    """
    p = Path(__file__).resolve()
    for d in [p, *p.parents]:
        f = d / name
        if f.exists():
            return f
    raise FileNotFoundError(name)

# Import local modules
try:
    from .frappe_utils import add_seren_to_frappe
except ImportError:
    from frappe_utils import add_seren_to_frappe

# Import existing modules
try:
    from .tg_crew import TGCrew
    from .reef import Reef
except ImportError:
    from tg_crew import TGCrew
    from reef import Reef

app = typer.Typer(help="Reef - Telegram Crew Manager")
console = Console()

@app.callback(invoke_without_command=True)
def main(ctx: typer.Context):
    """Reef - Telegram Crew Manager

    Automatically ensures commands run from the frappe-bench directory
    to prevent Frappe logging issues.
    """
    # Always ensure we're in the frappe-bench directory
    ensure_frappe_bench_cwd()

    # If no command was provided, show help
    if ctx.invoked_subcommand is None:
        console.print(app.get_help(ctx))
        raise typer.Exit()

# Global variable to maintain a single TGCrew instance across all commands
_crew_instance = None

def get_crew() -> TGCrew:
    """Initialize and return TGCrew instance.

    This function ensures we maintain a single TGCrew instance across all commands
    to properly track worker state.
    """
    global _crew_instance

    if _crew_instance is None:
        try:
            console.print("[dim]Initializing TGCrew...[/dim]")
            _crew_instance = TGCrew()

            # Log the number of workers loaded
            worker_count = len(_crew_instance.seren_workers)
            console.print(f"[dim]Loaded {worker_count} worker{'s' if worker_count != 1 else ''} from config[/dim]")

        except Exception as e:
            console.print(f"[red]Error initializing TGCrew: {e}[/red]")
            import traceback
            console.print(f"[dim]{traceback.format_exc()}[/dim]")
            raise typer.Exit(1)

    return _crew_instance

@app.command()
def start(
    worker: str = typer.Argument(..., help="Worker name or 'all' to start all workers"),
    config: Path = typer.Option(
        config_path(),
        "--config", "-c",
        help="Path to configuration file",
        exists=True, file_okay=True, dir_okay=False, readable=True
    )
):
    """Start a specific worker or all workers."""
    crew = get_crew()

    async def _start_worker(worker_name: str):
        worker = crew.get_worker(worker_name)
        if not worker:
            console.print(f"[yellow]Worker '{worker_name}' not found[/yellow]")
            return False

        # Get the current worker state before starting
        console.print(f"[dim]Worker state before start: is_running={worker.is_running()}, has_client={worker.client is not None}[/dim]")

        if worker.is_running():
            console.print(f"[yellow]Worker '{worker_name}' is already running[/yellow]")
            return True

        try:
            # Start the worker and get the result
            success = await worker.start()

            # Log the worker state after starting
            console.print(f"[dim]Worker state after start: is_running={worker.is_running()}, has_client={worker.client is not None}[/dim]")

            # Verify the worker is actually running
            is_actually_running = worker.is_running()
            console.print(f"[dim]Verification: is_actually_running={is_actually_running}[/dim]")

            if success and is_actually_running:
                console.print(f"[green]✓ Successfully started worker '{worker_name}'[/green]")
                return True
            else:
                error_msg = "Unknown error"
                if not success:
                    error_msg = "worker.start() returned False"
                elif not is_actually_running:
                    error_msg = "worker.is_running() returned False after start"
                console.print(f"[red]✗ Failed to start worker '{worker_name}': {error_msg}[/red]")

                # Additional debug info
                console.print(f"[dim]Worker attributes: {dir(worker)}")
                if hasattr(worker, 'client'):
                    console.print(f"[dim]Client attributes: {dir(worker.client) if worker.client else 'No client'}")

                return False

        except Exception as e:
            console.print(f"[red]✗ Error starting worker '{worker_name}': {e}[/red]")
            import traceback
            console.print(f"[dim]{traceback.format_exc()}[/dim]")
            return False

    async def _start_all():
        workers = crew.seren_workers
        if not workers:
            console.print("[yellow]No workers configured[/yellow]")
            return False

        success_count = 0
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Starting workers...", total=len(workers))
            for worker in workers:
                if worker.enabled:
                    if await worker.start():
                        success_count += 1
                        progress.print(f"[green]✓[/green] Started {worker.name}")
                    else:
                        progress.print(f"[red]✗[/red] Failed to start {worker.name}")
                progress.advance(task)

        console.print(f"\n[bold]Started {success_count} out of {len(workers)} workers[/bold]")
        return success_count > 0

    try:
        if worker.lower() == 'all':
            asyncio.run(_start_all())
        else:
            success = asyncio.run(_start_worker(worker))
            if not success:
                raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)

@app.command()
def stop(
    worker: str = typer.Argument(..., help="Worker name or 'all' to stop all workers"),
    force: bool = typer.Option(
        False, "--force", "-f",
        help="Force stop without waiting for clean shutdown"
    )
):
    """Stop a specific worker or all workers."""
    crew = get_crew()

    async def _stop_worker(worker_name: str):
        worker = crew.get_worker(worker_name)
        if not worker:
            console.print(f"[yellow]Worker '{worker_name}' not found[/yellow]")
            return False

        if not worker.is_running():
            console.print(f"[yellow]Worker '{worker_name}' is not running[/yellow]")
            return True

        try:
            await worker.stop()
            console.print(f"[green]Stopped worker '{worker_name}'[/green]")
            return True
        except Exception as e:
            console.print(f"[red]Error stopping worker '{worker_name}': {e}[/red]")
            return False

    async def _stop_all():
        workers = crew.get_active_workers()
        if not workers:
            console.print("[yellow]No active workers found[/yellow]")
            return False

        success_count = 0
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Stopping workers...", total=len(workers))
            for worker in workers:
                try:
                    await worker.stop()
                    success_count += 1
                    progress.print(f"[green]✓[/green] Stopped {worker.name}")
                except Exception as e:
                    progress.print(f"[red]✗[/red] Failed to stop {worker.name}: {e}")
                progress.advance(task)

        console.print(f"\n[bold]Stopped {success_count} out of {len(workers)} workers[/bold]")
        return success_count > 0

    try:
        if worker.lower() == 'all':
            asyncio.run(_stop_all())
        else:
            success = asyncio.run(_stop_worker(worker))
            if not success:
                raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)

@app.command()
def status(debug: bool = typer.Option(False, "--debug", "-d", help="Show debug information")):
    """Show status of all workers."""
    crew = get_crew()
    status_data = crew.get_worker_status()

    # Create a table for worker status
    table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
    table.add_column("Worker", style="dim", width=20)
    table.add_column("Status", width=15)
    table.add_column("Phone Number", width=15)
    table.add_column("Enabled", justify="center")
    table.add_column("Client", justify="center")
    table.add_column("PID", width=10)
    table.add_column("Session", style="dim")

    running_count = 0
    enabled_count = 0

    for worker_name, info in status_data.items():
        if 'error' in info:
            # Handle error state
            status_icon = "⚠️"
            status_text = f"Error: {info['error']}"
            status_style = "yellow"
            pid = info.get('pid', 'N/A')
        else:
            # Normal status display
            is_running = info.get('running', False)
            status_icon = "🟢" if is_running else "🔴"
            status_text = "Running" if is_running else "Stopped"
            status_style = "green" if is_running else "red"
            pid = info.get('pid', 'N/A')

            if info.get('enabled', False):
                enabled_count += 1
                if is_running:
                    running_count += 1

        phone = info.get('phone_number', 'N/A')
        enabled = "✅" if info.get('enabled', False) else "❌"
        client = "✅" if info.get('client_status') == 'connected' else "❌"
        session = info.get('session_name', 'N/A')

        table.add_row(
            worker_name,
            Text(f"{status_icon} {status_text}", style=status_style),
            phone,
            enabled,
            client,
            str(pid),
            session
        )

        # Add debug information if requested
        if debug and 'debug' in info:
            debug_info = info['debug']
            debug_text = "\n".join([f"  [dim]{k}:[/dim] {v}" for k, v in debug_info.items()])
            table.add_row(
                "", "", "", "", "", "", Panel(debug_text, border_style="dim", padding=(0, 1))
            )

    # Create a panel with the table
    panel = Panel.fit(
        table if table.rows else "No workers configured",
        title="Worker Status",
        border_style="blue",
        padding=(1, 2)
    )

    console.print(panel)

    # Print summary
    disabled_count = len(status_data) - enabled_count
    summary_text = f"[bold]Summary:[/bold] {running_count} running, {enabled_count - running_count} stopped, {enabled_count} enabled, {disabled_count} disabled"
    if running_count > 0:
        summary_text += " 🚀"
    console.print(summary_text)

def prompt_for_2fa() -> str:
    """Prompt user for 2FA password."""
    return typer.prompt("Enter your 2FA password", hide_input=True)

async def get_code(phone: str) -> str:
    """Helper function to get the Telegram verification code from user input."""
    return input(f"Enter the code sent to {phone}: ")

class PasswordPrompt:
    """Helper class to handle Telegram's password prompt."""
    async def __call__(self):
        return typer.prompt("Enter your 2FA password", hide_input=True)

async def _add_seren_async():
    """Interactively add a new Seren account to the crew."""
    from telethon import TelegramClient
    import os
    import yaml

    # Get configuration file path
    try:
        config_file = config_path()
    except FileNotFoundError:
        typer.echo("Error: Configuration file 'tg-crew.yml' not found in any parent directory.")
        raise typer.Exit(1)

    # Load existing config
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f) or {}

    # Get global settings or initialize if not present
    global_settings = config.setdefault('global_settings', {})
    data_dir = global_settings.get('data_dir', 'TGCrewData')

    # Ensure data directory exists
    os.makedirs(data_dir, exist_ok=True)

    # Get worker list or initialize if not present
    workers = config.setdefault('seren_workers', [])

    # Collect Seren details
    typer.echo("\n[bold]Add a new Seren account[/bold]")
    typer.echo("=" * 50)

    # Get required information
    name = typer.prompt("Enter a name for this Seren (alphanumeric, no spaces)").strip()

    # Check if name already exists
    existing_seren = next((w for w in workers if w.get('name') == name), None)
    if existing_seren:
        # Check if the session file exists
        worker_dir = os.path.join(data_dir, name)
        session_file = os.path.join(worker_dir, "bot.session")

        if not os.path.exists(session_file):
            # If session file doesn't exist, remove the old entry
            workers.remove(existing_seren)
            typer.echo(f"[yellow]Found stale Seren '{name}' without session file. Removing...[/yellow]")
        else:
            typer.echo(f"[red]Error: A Seren with name '{name}' already exists.[/red]")
            raise typer.Exit(1)

    api_id = typer.prompt("Enter your API ID")
    api_hash = typer.prompt("Enter your API Hash")
    phone_number = typer.prompt("Enter your phone number (with country code, e.g., +1234567890)")

    # Create the worker's data directory first with proper permissions
    worker_dir = os.path.join(data_dir, name)
    os.makedirs(worker_dir, exist_ok=True)
    os.chmod(worker_dir, 0o700)  # Ensure directory is writable

    # Define the final session path (Telethon adds .session automatically)
    final_session = os.path.join(worker_dir, "bot")

    # Set the session file path with .session extension
    session_file = f"{final_session}.session"

    # If session file exists, remove it to start fresh
    if os.path.exists(session_file):
        try:
            os.remove(session_file)
        except Exception as e:
            typer.echo(f"[yellow]Warning: Could not remove existing session file: {e}[/yellow]")

    # Use the final session path directly
    temp_session = final_session

    # Create the client with proper session path and configuration
    client = TelegramClient(
        temp_session,
        int(api_id),  # Ensure api_id is an integer
        api_hash,
        device_model="Reef CLI",
        system_version="1.0",
        app_version="1.0.0",
        system_lang_code="en",
        lang_code="en"
    )

    try:
        # Connect and handle authentication
        await client.connect()

        # Request code
        sent = await client.send_code_request(phone_number)

        # Sign in with the code
        try:
            code = await get_code(phone_number)
            await client.sign_in(phone_number, code, phone_code_hash=sent.phone_code_hash)

            # Ensure we're properly authorized
            if not await client.is_user_authorized():
                raise Exception("Failed to authorize user after code verification")

        except Exception as e:
            if "two-steps" in str(e).lower():
                password = await typer.prompt("Enter your 2FA password", hide_input=True)
                await client.sign_in(password=password)
            else:
                raise

        # At this point, we're authenticated - save the session explicitly
        # (avoid disconnect/reconnect here to prevent race conditions with the session file)
        client.session.save()

        # Ensure we're still authorized before proceeding
        if not await client.is_user_authorized():
            raise Exception("Failed to authorize user after saving session")

        # If we get here, authentication was successful
        typer.echo("\n[green]✓ Successfully authenticated with Telegram![/green]")

        # Create new worker config
        new_worker = {
            'name': name,
            'api_id': api_id,
            'api_hash': api_hash,
            'phone_number': phone_number,
            'session_name': "bot",
            'enabled': True,
            'data_dir': data_dir
        }

        # Add to config
        workers.append(new_worker)

        # Save the updated configuration
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)

        typer.echo(f"\n[green]✓ Successfully added {name} to {config_file}[/green]")

        # Verify the session file was created
        session_file = f"{temp_session}.session"

        if os.path.exists(session_file):
            try:
                # Set proper permissions
                os.chmod(session_file, 0o600)  # rw-------

                # Verify we can read the file
                with open(session_file, 'rb') as f:
                    if len(f.read()) > 0:
                        typer.echo(f"[green]✓ Session file saved to: {session_file}[/green]")
                        new_worker['session_name'] = os.path.basename(temp_session)
                        os.chmod(session_file, 0o600)
                    else:
                        typer.echo(f"[red]Error: Session file is empty at {session_file}[/red]")
            except Exception as e:
                typer.echo(f"[red]Error accessing session file: {e}[/red]")
        else:
            # Try to find any .session files that might have been created
            import glob
            session_files = glob.glob(os.path.join(worker_dir, "*.session"))
            if session_files:
                typer.echo(f"[yellow]Found session file at unexpected location: {session_files[0]}[/yellow]")
                try:
                    # Use the first found session file
                    os.rename(session_files[0], session_file)
                    os.chmod(session_file, 0o600)
                    typer.echo(f"[green]✓ Moved session file to: {session_file}[/green]")
                    new_worker['session_name'] = os.path.basename(temp_session)
                except Exception as e:
                    typer.echo(f"[red]Error moving session file: {e}[/red]")
            else:
                typer.echo(f"[red]Error: No session file was created. Check Telegram authentication.[/red]")

        # Register agent with Frappe using settings from config
        frappe_settings = global_settings.get('frappe', {})
        if frappe_settings:
            agent_data = {
                "agent_name": name,
                "title": f"Seren: {name}",
                "telegram_api_id": api_id,
                "telegram_api_hash": api_hash,
                "telegram_phone": phone_number,
                "telegram_session_name": "bot",
                "assigned_to": "Administrator"
            }

            try:
                result = add_seren_to_frappe(
                    # base_url=frappe_settings.get('url'),
                    # auth_token=frappe_settings.get('api_token'),
                    agent=agent_data
                )
                typer.echo(f"\n[green]✓ Successfully registered agent with Frappe![/green]")
                typer.echo(f"Agent ID: {result.get('data', {}).get('name')}")
            except Exception as e:
                typer.echo(f"\n[yellow]Warning: Could not register with Frappe: {str(e)}[/yellow]")

        typer.echo(f"\n[bold]Next steps:[/bold]")
        typer.echo(f"1. Start the worker: [cyan]reef start {name}[/cyan]")
        typer.echo(f"2. Check status: [cyan]reef status[/cyan]")

    except Exception as e:
        typer.echo(f"\n[red]Error adding Seren: {e}[/red]")
        # Clean up on error
        try:
            if os.path.exists(f"{temp_session}.session"):
                os.remove(f"{temp_session}.session")
        except:
            pass
        raise typer.Exit(1)
    finally:
        if 'client' in locals():
            try:
                if client.is_connected():
                    await client.disconnect()
            except Exception as e:
                typer.echo(f"[yellow]Warning during disconnect: {e}[/yellow]")

@app.command()
def add_seren():
    """Interactively add a new Seren account to the crew."""
    asyncio.run(_add_seren_async())

# Typer doesn't support async commands directly, so we need this wrapper
# to handle the async command execution
def main():
    """Main entry point for the CLI."""
    app()

if __name__ == "__main__":
    app()
