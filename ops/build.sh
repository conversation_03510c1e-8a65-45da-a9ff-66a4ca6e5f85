
#!/bin/bash
set -euo pipefail

# VARIABLES
LOG_FILE=
FRAPPE_DOCKER_PATH="./frappe_docker"
APPS_PATH="$(cd "$(dirname "$0")" && pwd)/apps.json"
FRAPPE_REPO="https://github.com/frappe/frappe"
FRAPPE_BRANCH="version-15"
IMAGE_TAG="frappe-agent:dev"
NAMESPACE="slifedev"
DOCKER_USERNAME="slifedev"
DOCKER_PASSWORD="************************************"


# FUNCTIONS
log() {
  if [ -n "${LOG_FILE:-}" ]; then
    echo -e "\033[0;34m$(date +'%Y-%m-%d %H:%M:%S') - $1\033[0m" | tee -a "$LOG_FILE"
  else
    echo -e "\033[0;34m$(date +'%Y-%m-%d %H:%M:%S') - $1\033[0m"
  fi
}

error_exit() {
  echo -e "\033[0;31m$(date +'%Y-%m-%d %H:%M:%S') - ERROR $1\033[0m" | tee -a "${LOG_FILE:-}"
  exit 1
}

warn() {
  echo -e "\033[0;31m$(date +'%Y-%m-%d %H:%M:%S') - WARNING $1\033[0m" | tee -a "${LOG_FILE:-}"
}

# Set platform after functions
PLATFORM=${PLATFORM:-linux/amd64}
log "Using target platform: $PLATFORM"

# OPERATIONS
log "cd $FRAPPE_DOCKER_PATH"
cd "$FRAPPE_DOCKER_PATH" || error_exit "Failed to cd $FRAPPE_DOCKER_PATH"

if [ ! -f "$APPS_PATH" ]; then
  error_exit "apps.json not found at $APPS_PATH"
fi
log "Using apps.json at: $APPS_PATH"
log "Generating base64 string from $APPS_PATH"
APPS_JSON_BASE64=$(base64 < "$APPS_PATH" | tr -d '\n') || error_exit "Failed to generate base64 string from $APPS_PATH"

log "Building an image"
#docker buildx build --no-cache --load \
#  --platform="$PLATFORM" \
#  --build-arg=FRAPPE_PATH="$FRAPPE_REPO" \
#  --build-arg=FRAPPE_BRANCH="$FRAPPE_BRANCH" \
#  --build-arg=PYTHON_VERSION=3.10.12 \
#  --build-arg=NODE_VERSION=20.11.0 \
#  --build-arg=APPS_JSON_BASE64="$APPS_JSON_BASE64" \
#  -t "$IMAGE_TAG" \
#  -f images/custom/Containerfile . || error_exit "Failed to build an image"

log "Docker login"
echo "$DOCKER_PASSWORD" | docker login --username "$DOCKER_USERNAME" --password-stdin || error_exit "Failed to docker login"
log "Tag $IMAGE_TAG"
docker tag "$IMAGE_TAG" "$NAMESPACE/$IMAGE_TAG" || error_exit "Failed to tag $IMAGE_TAG"
log "Push $IMAGE_TAG"
docker push "$NAMESPACE/$IMAGE_TAG" || error_exit "Failed to push $NAMESPACE/$IMAGE_TAG"

log "Clear dangling images"
docker rmi $(docker images -f dangling=true -q) || true
log "Clear builder cache"
docker builder prune -f

printf "\033[0;32m%s/%s has been built successfully\033[0m\n" "$NAMESPACE" "$IMAGE_TAG"
