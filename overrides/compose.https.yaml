services:
  frontend:
    labels:
      - traefik.enable=true
      - traefik.http.services.frontend.loadbalancer.server.port=8080
      - traefik.http.routers.frontend-http.entrypoints=websecure
      - traefik.http.routers.frontend-http.tls.certresolver=main-resolver
      - traefik.http.routers.frontend-http.rule=Host(${SITES:?List of sites not set})

  proxy:
    image: traefik:v2.11
    command:
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
      - --entrypoints.websecure.address=:443
      - --certificatesResolvers.main-resolver.acme.httpChallenge=true
      - --certificatesResolvers.main-resolver.acme.httpChallenge.entrypoint=web
      - --certificatesResolvers.main-resolver.acme.email=${LETSENCRYPT_EMAIL:?No Let's Encrypt email set}
      - --certificatesResolvers.main-resolver.acme.storage=/letsencrypt/acme.json
    ports:
      - ${HTTP_PUBLISH_PORT:-80}:80
      - ${HTTPS_PUBLISH_PORT:-443}:443
    volumes:
      - cert-data:/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock:ro

volumes:
  cert-data:
