#!/bin/bash

# Usage: ./sync-frappe-utils.sh [LOCAL_DIRECTORY] [MINUTES]
# Example: ./sync-frappe-utils.sh ../ai_agent 10
# If no directory is provided, defaults to ".."
# If no minutes is provided, defaults to 5

# Configuration
DOCKER_CONTEXT="remote"
LOCAL_AI_AGENT_DIR="${1:-..}"  # Use first argument or default to ".."
MINUTES_BACK="${2:-50}"         # Use second argument or default to 5 minutes
CONTAINER_NAME="c9ac9a15418a"  # Using the first 12 characters of the container ID
CONTAINER_BASE_PATH="/home/<USER>/frappe-bench/apps/ai_agent"

# Normalize the local directory path to avoid path issues
LOCAL_AI_AGENT_DIR=$(realpath "$LOCAL_AI_AGENT_DIR")
LOCAL_AI_AGENT_DIR_WITH_SLASH="${LOCAL_AI_AGENT_DIR}/"

# Validate that the local directory exists
if [ ! -d "$LOCAL_AI_AGENT_DIR" ]; then
    echo "❌ Error: Local directory '$LOCAL_AI_AGENT_DIR' does not exist!"
    echo "Usage: $0 [LOCAL_DIRECTORY] [MINUTES]"
    echo "Example: $0 ../ai_agent 10"
    echo "If no directory is provided, defaults to '..' (parent directory)"
    echo "If no minutes is provided, defaults to 5 minutes"
    exit 1
fi

# Validate minutes parameter
if ! [[ "$MINUTES_BACK" =~ ^[0-9]+$ ]] || [ "$MINUTES_BACK" -le 0 ]; then
    echo "❌ Error: Minutes must be a positive integer!"
    echo "Usage: $0 [LOCAL_DIRECTORY] [MINUTES]"
    echo "Example: $0 ../ai_agent 10"
    exit 1
fi

echo "📁 Local directory: $LOCAL_AI_AGENT_DIR"
echo "🐳 Container path: $CONTAINER_BASE_PATH"
echo "⏰ Looking for files modified in the last $MINUTES_BACK minutes"
echo ""

# Check if remote context is available
if ! docker context use $DOCKER_CONTEXT &> /dev/null; then
    echo "Error: Could not switch to Docker context '$DOCKER_CONTEXT'"
    exit 1
fi

# Verify container exists
if ! docker ps -q --no-trunc | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container $CONTAINER_NAME not found in context $DOCKER_CONTEXT"
    echo "Available containers:"
    docker ps -a
    exit 1
fi

# Function to get relative path safely
get_relative_path() {
    local file="$1"
    local relative_path=""

    # Check if file starts with our directory path
    if [[ "$file" == "$LOCAL_AI_AGENT_DIR_WITH_SLASH"* ]]; then
        relative_path="${file#$LOCAL_AI_AGENT_DIR_WITH_SLASH}"
    elif [[ "$file" == "$LOCAL_AI_AGENT_DIR" ]]; then
        relative_path=""
    else
        # Fallback: use basename if path doesn't match expected pattern
        relative_path=$(basename "$file")
    fi

    echo "$relative_path"
}

# Function to check if a file should be excluded
should_exclude_file() {
    local file="$1"
    local relative_path=$(get_relative_path "$file")

    # Skip if file starts with dot (hidden files)
    if [[ "$relative_path" == .* ]]; then
        return 0
    fi

    # Common directories and files to exclude
    local exclude_patterns=(
        ".git/*"
        ".idea/*"
        ".vscode/*"
        ".vs/*"
        "node_modules/*"
        "__pycache__/*"
        ".pytest_cache/*"
        ".mypy_cache/*"
        ".tox/*"
        "venv/*"
        "env/*"
        ".env/*"
        "dist/*"
        "build/*"
        "*.egg-info/*"
        ".DS_Store"
        "Thumbs.db"
        "*.pyc"
        "*.pyo"
        "*.pyd"
        "*.so"
        "*.dylib"
        "*.dll"
        "*.log"
        "*.tmp"
        "*.temp"
        "*.swp"
        "*.swo"
        "*~"
        "*.bak"
        "*.orig"
        ".coverage"
        "coverage.xml"
        "*.lcov"
        ".nyc_output/*"
        "*.min.js"
        "*.min.css"
        "*.map"
    )

    # Check against exclude patterns
    for pattern in "${exclude_patterns[@]}"; do
        if [[ "$relative_path" == $pattern ]]; then
            return 0
        fi
    done

    return 1
}

# Function to check gitignore
is_gitignored() {
    local file="$1"
    local git_dir="$LOCAL_AI_AGENT_DIR"

    # Check if we're in a git repository
    if [ -d "$git_dir/.git" ] || git -C "$git_dir" rev-parse --git-dir >/dev/null 2>&1; then
        # Use git check-ignore to see if file should be ignored
        if git -C "$git_dir" check-ignore "$file" >/dev/null 2>&1; then
            return 0
        fi
    fi

    return 1
}

# Find all files modified in the specified time period
echo "🔍 Finding files modified in the last $MINUTES_BACK minutes in: $LOCAL_AI_AGENT_DIR"
ALL_MODIFIED_FILES=$(find "$LOCAL_AI_AGENT_DIR" -type f -mmin -"$MINUTES_BACK" 2>/dev/null)

# Filter out excluded files
MODIFIED_FILES=""
while IFS= read -r file; do
    if [ -n "$file" ] && [ -f "$file" ]; then
        if should_exclude_file "$file"; then
            echo "🚫 Excluding: $(get_relative_path "$file") (common exclude pattern)"
            continue
        fi

        if is_gitignored "$file"; then
            echo "🚫 Excluding: $(get_relative_path "$file") (gitignored)"
            continue
        fi

        if [ -z "$MODIFIED_FILES" ]; then
            MODIFIED_FILES="$file"
        else
            MODIFIED_FILES="$MODIFIED_FILES"$'\n'"$file"
        fi
    fi
done <<< "$ALL_MODIFIED_FILES"

if [ -z "$MODIFIED_FILES" ]; then
    echo "ℹ️  No syncable files modified in the last $MINUTES_BACK minutes found in $LOCAL_AI_AGENT_DIR"
    echo "    (excluded common development files, cache, and gitignored files)"
    docker context use desktop-linux &> /dev/null
    exit 0
fi

echo "📝 Found syncable modified files:"
echo "$MODIFIED_FILES"
echo ""

# Create base directory in container if it doesn't exist
echo "Ensuring base directory exists in container..."
docker exec "$CONTAINER_NAME" mkdir -p "$CONTAINER_BASE_PATH"

# Sync each modified file
echo "🚀 Syncing modified files to container..."
while IFS= read -r file; do
    if [ -f "$file" ]; then
        # Get relative path from the specified directory using our safe function
        RELATIVE_PATH=$(get_relative_path "$file")
        CONTAINER_FILE_PATH="$CONTAINER_BASE_PATH/$RELATIVE_PATH"
        CONTAINER_DIR_PATH=$(dirname "$CONTAINER_FILE_PATH")

        echo "📤 Syncing: $RELATIVE_PATH"

        # Create directory structure in container
        docker exec "$CONTAINER_NAME" mkdir -p "$CONTAINER_DIR_PATH"

        # Copy file to container
        cat "$file" | docker exec -i "$CONTAINER_NAME" bash -c "cat > '$CONTAINER_FILE_PATH'"

        if [ $? -eq 0 ]; then
            echo "  ✅ Successfully synced $RELATIVE_PATH"
        else
            echo "  ❌ Failed to sync $RELATIVE_PATH"
        fi
    fi
done <<< "$MODIFIED_FILES"

# Switch back to default context
docker context use desktop-linux &> /dev/null

echo ""
echo "🎉 File sync complete!"
echo "ℹ️  Note: Excluded common development files (.git, .idea, node_modules, __pycache__, etc.) and gitignored files"
