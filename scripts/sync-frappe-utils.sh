#!/bin/bash

# Usage: ./sync-frappe-utils.sh [LOCAL_DIRECTORY]
# Example: ./sync-frappe-utils.sh ../ai_agent
# If no directory is provided, defaults to ".."

# Set Docker context to remote
DOCKER_CONTEXT="remote"
LOCAL_AI_AGENT_DIR="${1:-..}"  # Use first argument or default to ".."
CONTAINER_NAME="c9ac9a15418a"  # Using the first 12 characters of the container ID
CONTAINER_BASE_PATH="/home/<USER>/frappe-bench/apps/ai_agent"

# Validate that the local directory exists
if [ ! -d "$LOCAL_AI_AGENT_DIR" ]; then
    echo "❌ Error: Local directory '$LOCAL_AI_AGENT_DIR' does not exist!"
    echo "Usage: $0 [LOCAL_DIRECTORY]"
    echo "Example: $0 ../ai_agent"
    echo "If no directory is provided, defaults to '..' (parent directory)"
    exit 1
fi

echo "📁 Local directory: $(realpath "$LOCAL_AI_AGENT_DIR")"
echo "🐳 Container path: $CONTAINER_BASE_PATH"
echo ""

# Check if remote context is available
if ! docker context use $DOCKER_CONTEXT &> /dev/null; then
    echo "Error: Could not switch to Docker context '$DOCKER_CONTEXT'"
    exit 1
fi

# Verify container exists
if ! docker ps -q --no-trunc | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container $CONTAINER_NAME not found in context $DOCKER_CONTEXT"
    echo "Available containers:"
    docker ps -a
    exit 1
fi

# Find all files modified in the last 5 minutes in the specified directory
echo "🔍 Finding files modified in the last 5 minutes in: $LOCAL_AI_AGENT_DIR"
MODIFIED_FILES=$(find "$LOCAL_AI_AGENT_DIR" -type f -mmin -50 2>/dev/null)

if [ -z "$MODIFIED_FILES" ]; then
    echo "ℹ️  No files modified in the last 5 minutes found in $LOCAL_AI_AGENT_DIR"
    docker context use desktop-linux &> /dev/null
    exit 0
fi

echo "📝 Found modified files:"
echo "$MODIFIED_FILES"
echo ""

# Create base directory in container if it doesn't exist
echo "Ensuring base directory exists in container..."
docker exec "$CONTAINER_NAME" mkdir -p "$CONTAINER_BASE_PATH"

# Sync each modified file
echo "🚀 Syncing modified files to container..."
while IFS= read -r file; do
    if [ -f "$file" ]; then
        # Get relative path from the specified directory
        RELATIVE_PATH="${file#$LOCAL_AI_AGENT_DIR/}"
        CONTAINER_FILE_PATH="$CONTAINER_BASE_PATH/$RELATIVE_PATH"
        CONTAINER_DIR_PATH=$(dirname "$CONTAINER_FILE_PATH")

        echo "📤 Syncing: $RELATIVE_PATH"

        # Create directory structure in container
        docker exec "$CONTAINER_NAME" mkdir -p "$CONTAINER_DIR_PATH"

        # Copy file to container
        cat "$file" | docker exec -i "$CONTAINER_NAME" bash -c "cat > '$CONTAINER_FILE_PATH'"

        if [ $? -eq 0 ]; then
            echo "  ✅ Successfully synced $RELATIVE_PATH"
        else
            echo "  ❌ Failed to sync $RELATIVE_PATH"
        fi
    fi
done <<< "$MODIFIED_FILES"

# Switch back to default context
docker context use desktop-linux &> /dev/null

echo ""
echo "🎉 File sync complete!"
